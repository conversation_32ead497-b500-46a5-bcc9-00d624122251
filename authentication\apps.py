"""
Authentication App Configuration

This module configures the authentication app and starts the scheduler
for Windows-compatible scheduled tasks.
"""

import logging
from django.apps import AppConfig

logger = logging.getLogger(__name__)


class AuthenticationConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "authentication"

    def ready(self):
        """
        Called when Django starts up.
        This is where we start the scheduler for scheduled tasks.
        """
        # Only start scheduler in main process (not in management commands)
        import os
        import sys

        # Check if this is the main Django process
        if os.environ.get('RUN_MAIN') == 'true' or 'runserver' not in sys.argv:
            # Don't start scheduler during migrations or other management commands
            if len(sys.argv) > 1 and sys.argv[1] in ['migrate', 'makemigrations', 'collectstatic', 'shell']:
                return

            try:
                from authentication.scheduler import start_scheduler
                start_scheduler()
                logger.info("Authentication app: Scheduler started successfully")
            except Exception as e:
                logger.error(f"Authentication app: Failed to start scheduler: {str(e)}")
                # Don't raise exception to prevent Django from failing to start
