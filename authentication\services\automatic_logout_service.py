"""
Automatic Logout Service

This service handles automatic logout functionality with proper cleanup
when JWT tokens expire. It integrates with the existing authentication
system and ensures complete session cleanup.
"""

import logging
from django.db import connection, transaction
from django.utils import timezone
from django.core.cache import cache

from authentication.models import TcdApp<PERSON>ember, TcdUserConsult
from authentication.services.token_expiration_service import TokenExpirationService
from authentication.utils import get_token_storage_hash
from utils.response import APIResponse

logger = logging.getLogger(__name__)


class AutomaticLogoutService:
    """
    Service for handling automatic logout with comprehensive cleanup
    """
    
    @staticmethod
    def perform_complete_logout(user_id, user_type, token_string=None, language='th'):
        """
        Perform complete logout with all cleanup operations
        
        Args:
            user_id (int): User ID
            user_type (str): User type ('member' or 'consultant')
            token_string (str, optional): Token string for blacklisting
            language (str): Language for messages
            
        Returns:
            dict: Logout operation result
        """
        try:
            logger.info(f"Performing complete logout for {user_type} user {user_id}")
            
            # Step 1: Clear user session data
            session_cleared = AutomaticLogoutService.clear_all_session_data(user_id, user_type)
            
            # Step 2: Blacklist all user tokens
            tokens_blacklisted = AutomaticLogoutService.blacklist_all_user_tokens(user_id, user_type)
            
            # Step 3: Clear cache data
            cache_cleared = AutomaticLogoutService.clear_user_cache_data(user_id, user_type)
            
            # Step 4: If specific token provided, ensure it's blacklisted
            specific_token_handled = True
            if token_string:
                specific_token_handled = AutomaticLogoutService.handle_specific_token(
                    token_string, user_id, user_type
                )
            
            success = session_cleared and tokens_blacklisted and cache_cleared and specific_token_handled
            
            result = {
                'success': success,
                'user_id': user_id,
                'user_type': user_type,
                'operations': {
                    'session_cleared': session_cleared,
                    'tokens_blacklisted': tokens_blacklisted,
                    'cache_cleared': cache_cleared,
                    'specific_token_handled': specific_token_handled
                }
            }
            
            if success:
                logger.info(f"Complete logout successful for {user_type} user {user_id}")
            else:
                logger.warning(f"Some logout operations failed for {user_type} user {user_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error performing complete logout: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'user_id': user_id,
                'user_type': user_type
            }
    
    @staticmethod
    def clear_all_session_data(user_id, user_type):
        """
        Clear all session-related data for a user
        
        Args:
            user_id (int): User ID
            user_type (str): User type
            
        Returns:
            bool: True if successful
        """
        try:
            logger.info(f"Clearing all session data for {user_type} user {user_id}")
            
            if user_type == 'member':
                # Clear member session data
                updated = TcdAppMember.objects.filter(id=user_id).update(
                    token_app=None,
                    checklogin=0  # Reset login status
                )
                if updated > 0:
                    logger.info(f"Cleared session data for member {user_id}")
                else:
                    logger.warning(f"No member found with ID {user_id}")
                    
            elif user_type == 'consultant':
                # Clear consultant session data
                updated = TcdUserConsult.objects.filter(id=user_id).update(
                    token_app=None
                )
                if updated > 0:
                    logger.info(f"Cleared session data for consultant {user_id}")
                else:
                    logger.warning(f"No consultant found with ID {user_id}")
                    
            else:
                logger.error(f"Unknown user type: {user_type}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error clearing session data: {str(e)}")
            return False
    
    @staticmethod
    def blacklist_all_user_tokens(user_id, user_type):
        """
        Blacklist all outstanding tokens for a user
        
        Args:
            user_id (int): User ID
            user_type (str): User type
            
        Returns:
            bool: True if successful
        """
        try:
            logger.info(f"Blacklisting all tokens for {user_type} user {user_id}")
            
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Find all outstanding tokens for this user that aren't already blacklisted
                    cursor.execute("""
                        SELECT ot.id, ot.jti
                        FROM tcd_outstanding_tokens ot
                        WHERE ot.user_id = %s AND ot.user_type = %s
                        AND NOT EXISTS (
                            SELECT 1 FROM tcd_blacklisted_tokens bt
                            WHERE bt.token_id = ot.id
                        )
                    """, [user_id, user_type])
                    
                    outstanding_tokens = cursor.fetchall()
                    
                    if not outstanding_tokens:
                        logger.info(f"No outstanding tokens found for {user_type} user {user_id}")
                        return True
                    
                    # Blacklist each token
                    blacklisted_count = 0
                    for token_id, jti in outstanding_tokens:
                        try:
                            cursor.execute("""
                                INSERT INTO tcd_blacklisted_tokens (token_id, blacklisted_at)
                                VALUES (%s, %s)
                            """, [token_id, timezone.now()])
                            
                            blacklisted_count += 1
                            logger.info(f"Blacklisted token {jti} for user {user_id}")
                            
                        except Exception as e:
                            logger.error(f"Failed to blacklist token {jti}: {str(e)}")
                    
                    logger.info(f"Blacklisted {blacklisted_count}/{len(outstanding_tokens)} tokens for user {user_id}")
                    return blacklisted_count == len(outstanding_tokens)
                    
        except Exception as e:
            logger.error(f"Error blacklisting user tokens: {str(e)}")
            return False
    
    @staticmethod
    def clear_user_cache_data(user_id, user_type):
        """
        Clear cached data for a user
        
        Args:
            user_id (int): User ID
            user_type (str): User type
            
        Returns:
            bool: True if successful
        """
        try:
            logger.info(f"Clearing cache data for {user_type} user {user_id}")
            
            # Clear user-specific cache keys
            cache_keys = [
                f"user_session_{user_type}_{user_id}",
                f"user_tokens_{user_type}_{user_id}",
                f"user_profile_{user_type}_{user_id}",
                f"user_permissions_{user_type}_{user_id}"
            ]
            
            for key in cache_keys:
                try:
                    cache.delete(key)
                    logger.debug(f"Cleared cache key: {key}")
                except Exception as e:
                    logger.warning(f"Failed to clear cache key {key}: {str(e)}")
            
            logger.info(f"Cache cleanup completed for {user_type} user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing cache data: {str(e)}")
            return False
    
    @staticmethod
    def handle_specific_token(token_string, user_id, user_type):
        """
        Handle a specific token (ensure it's blacklisted)
        
        Args:
            token_string (str): Token string
            user_id (int): User ID
            user_type (str): User type
            
        Returns:
            bool: True if successful
        """
        try:
            import jwt
            from authentication.views import format_jti_for_database
            
            # Decode token to get JTI
            unverified_payload = jwt.decode(
                token_string,
                options={"verify_signature": False, "verify_exp": False}
            )
            
            jti = unverified_payload.get('jti')
            if not jti:
                logger.warning("Token has no JTI, cannot blacklist specifically")
                return True  # Not a failure, just no specific action needed
            
            token_hash = get_token_storage_hash(token_string)
            jti_formatted = format_jti_for_database(jti)
            
            with connection.cursor() as cursor:
                # Find the specific token
                cursor.execute("""
                    SELECT id FROM tcd_outstanding_tokens
                    WHERE jti = CONVERT(UNIQUEIDENTIFIER, %s) AND token = %s
                """, [jti_formatted, token_hash])
                
                result = cursor.fetchone()
                if not result:
                    logger.warning(f"Specific token not found in outstanding tokens: {jti_formatted}")
                    return True  # Token might already be cleaned up
                
                token_id = result[0]
                
                # Check if already blacklisted
                cursor.execute("""
                    SELECT COUNT(*) FROM tcd_blacklisted_tokens WHERE token_id = %s
                """, [token_id])
                
                if cursor.fetchone()[0] > 0:
                    logger.info(f"Specific token {jti_formatted} is already blacklisted")
                    return True
                
                # Blacklist the specific token
                cursor.execute("""
                    INSERT INTO tcd_blacklisted_tokens (token_id, blacklisted_at)
                    VALUES (%s, %s)
                """, [token_id, timezone.now()])
                
                logger.info(f"Successfully blacklisted specific token {jti_formatted}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling specific token: {str(e)}")
            return False
    
    @staticmethod
    def handle_expired_token_request(token_string, language='th'):
        """
        Handle a request with an expired token - perform automatic logout
        
        Args:
            token_string (str): The expired token string
            language (str): Language for response
            
        Returns:
            APIResponse: Error response with logout information
        """
        try:
            # Check if token is expired
            is_expired, expiry_time, error = TokenExpirationService.is_token_expired(token_string)
            
            if error:
                return APIResponse.error(
                    error_code=1005,  # Invalid token
                    data={'reason': error},
                    language=language,
                    status_code=401
                )
            
            if not is_expired:
                # Token is not expired, this shouldn't be called
                logger.warning("handle_expired_token_request called with non-expired token")
                return None
            
            # Extract user information from expired token
            import jwt
            unverified_payload = jwt.decode(
                token_string,
                options={"verify_signature": False, "verify_exp": False}
            )
            
            user_id = unverified_payload.get('user_id')
            user_type = unverified_payload.get('user_type')
            
            if user_id and user_type:
                # Perform automatic logout
                logout_result = AutomaticLogoutService.perform_complete_logout(
                    user_id, user_type, token_string, language
                )
                
                return APIResponse.error(
                    error_code=1006,  # Token expired
                    data={
                        'reason': 'Token has expired - automatic logout performed',
                        'expired_at': expiry_time.isoformat() if expiry_time else None,
                        'auto_logout_performed': logout_result.get('success', False),
                        'user_id': user_id,
                        'user_type': user_type
                    },
                    language=language,
                    status_code=401
                )
            else:
                return APIResponse.error(
                    error_code=1005,  # Invalid token
                    data={'reason': 'Cannot extract user information from expired token'},
                    language=language,
                    status_code=401
                )
                
        except Exception as e:
            logger.error(f"Error handling expired token request: {str(e)}")
            return APIResponse.error(
                error_code=5000,  # Server error
                data={'reason': f'Failed to handle expired token: {str(e)}'},
                language=language,
                status_code=500
            )
