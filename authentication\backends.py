from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken, AuthenticationFailed
from rest_framework.authentication import get_authorization_header
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from django.conf import settings
from django.contrib.auth.models import AnonymousUser

from authentication.models import TcdAppMember, TcdUserConsult
from MCDC.models import TcdUsers
from authentication.services.token_expiration_service import TokenExpirationService
from authentication.services.automatic_logout_service import AutomaticLogoutService

import logging
logger = logging.getLogger(__name__)


class CustomJWTAuthentication(JWTAuthentication):
    """
    Custom JWT Authentication class that can handle both Member and Consultant authentication
    และตรวจสอบ token blacklist
    """
    
    def get_user(self, validated_token):
        """
        Get the user from the validated token based on user_type
        """
        try:
            user_id = validated_token.get('user_id')
            user_type = validated_token.get('user_type')
            
            # logger.info(f"JWT Authentication - user_id: {user_id}, user_type: {user_type}")
            # logger.info(f"JWT Authentication - Full token payload: {validated_token}")
            
            if not user_id or not user_type:
                logger.error("JWT Authentication - No user identification in token")
                raise InvalidToken(_('Token contained no recognizable user identification'))
            
            if user_type == 'member':
                try:
                    user = TcdAppMember.objects.get(id=user_id)
                    logger.info(f"JWT Authentication - Found member: {user.username}")

                    # ตรวจสอบสถานะ member - ห้าม login หากถูกลบ
                    from authentication.constants import MemberStatus
                    if user.status == MemberStatus.DELETED:
                        logger.error(f"JWT Authentication - Member account deleted: {user.username}")
                        raise AuthenticationFailed(_('This account has been deleted.'))

                    # ตรวจสอบสถานะ member อื่นๆ
                    if not MemberStatus.is_active(user.status):
                        logger.error(f"JWT Authentication - Member account disabled: {user.username}, status: {user.status}")
                        raise AuthenticationFailed(_('User account is disabled.'))
                    
                    # ตรวจสอบ lockout
                    from django.utils import timezone
                    if user.lockout_end_date and user.lockout_end_date > timezone.now():
                        logger.error(f"JWT Authentication - Member account locked: {user.username}")
                        raise AuthenticationFailed(_('Your account is temporarily locked. Please try again later.'))
                    
                    # เพิ่มข้อมูล user_type ให้กับ user object
                    user.user_type = 'member'
                    
                    # สำคัญ: ต้องเซ็ตเป็น True แบบเขียนตรงๆ ไม่ใช่ใช้ตัวแปร
                    user.is_authenticated = True
                    setattr(user, '_is_authenticated', True)
                    
                    # เพิ่มเมธอดที่จำเป็นสำหรับ Django auth
                    self._add_auth_methods(user)
                    
                    logger.info(f"JWT Authentication - Member authenticated successfully: {user.username}")
                    return user
                    
                except TcdAppMember.DoesNotExist:
                    logger.error(f"JWT Authentication - Member not found: {user_id}")
                    raise InvalidToken(_('User not found'))
                    
            elif user_type == 'consultant':
                try:
                    user = TcdUserConsult.objects.get(id=user_id)
                    logger.info(f"JWT Authentication - Found consultant: {user.username}")

                    # เพิ่มข้อมูล user_type ให้กับ user object
                    user.user_type = 'consultant'

                    # สำคัญ: ต้องเซ็ตเป็น True แบบเขียนตรงๆ ไม่ใช่ใช้ตัวแปร
                    user.is_authenticated = True
                    setattr(user, '_is_authenticated', True)

                    # เพิ่มเมธอดที่จำเป็นสำหรับ Django auth
                    self._add_auth_methods(user)

                    logger.info(f"JWT Authentication - Consultant authenticated successfully: {user.username}")
                    logger.info(f"JWT Authentication - is_authenticated set to: {user.is_authenticated}")
                    return user

                except TcdUserConsult.DoesNotExist:
                    logger.error(f"JWT Authentication - Consultant not found: {user_id}")
                    raise InvalidToken(_('User not found'))

            elif user_type == 'staff':
                try:
                    user = TcdUsers.objects.get(id=user_id)
                    logger.info(f"JWT Authentication - Found staff: {user.username}")

                    # เพิ่มข้อมูล user_type ให้กับ user object
                    user.user_type = 'staff'

                    # สำคัญ: ต้องเซ็ตเป็น True แบบเขียนตรงๆ ไม่ใช่ใช้ตัวแปร
                    user.is_authenticated = True
                    setattr(user, '_is_authenticated', True)

                    # เพิ่มเมธอดที่จำเป็นสำหรับ Django auth
                    self._add_auth_methods(user)

                    logger.info(f"JWT Authentication - Staff authenticated successfully: {user.username}")
                    logger.info(f"JWT Authentication - is_authenticated set to: {user.is_authenticated}")
                    return user

                except TcdUsers.DoesNotExist:
                    logger.error(f"JWT Authentication - Staff not found: {user_id}")
                    raise InvalidToken(_('User not found'))
            else:
                logger.error(f"JWT Authentication - Invalid user type: {user_type}")
                raise InvalidToken(_('Invalid user type'))
                
        except KeyError as e:
            logger.error(f"JWT Authentication - KeyError: {str(e)}")
            raise InvalidToken(_('Token contained no recognizable user identification'))
    
    def _add_auth_methods(self, user):
        """
        Add Django auth methods to user model
        """
        # Define methods required by Django auth
        def get_all_permissions(obj=None):
            return set()

        def has_perm(perm, obj=None):
            return False

        def has_perms(perm_list, obj=None):
            return False

        def has_module_perms(app_label):
            return False

        # Make sure is_anonymous is False
        user.is_anonymous = False
        
        # Instead of using a property, which causes setter issues,
        # use a direct attribute for is_authenticated
        user.is_authenticated = True
        
        # Also set _is_authenticated as a direct attribute for our custom checks
        setattr(user, '_is_authenticated', True)
        
        # Add methods if they don't exist
        if not hasattr(user, 'get_all_permissions'):
            user.get_all_permissions = get_all_permissions
        if not hasattr(user, 'has_perm'):
            user.has_perm = has_perm
        if not hasattr(user, 'has_perms'):
            user.has_perms = has_perms
        if not hasattr(user, 'has_module_perms'):
            user.has_module_perms = has_module_perms
            
        return user
    
    def authenticate(self, request):
        """
        Authenticate the request and return a tuple of (user, token).
        """
        logger.info(f"JWT Authentication - Starting authentication for path: {request.path}")
        header = get_authorization_header(request)
        
        if not header:
            logger.info("JWT Authentication - No authorization header found")
            return None
            
        try:
            # Get the raw token
            raw_token = self.get_raw_token(header)
            if raw_token is None:
                logger.info("JWT Authentication - No valid token found in header")
                return None
                
            # logger.info("JWT Authentication - Raw token extracted successfully")
            # logger.info(f"JWT Authentication - Raw token: {raw_token}")
            
            # Validate the token
            try:
                validated_token = self.get_validated_token(raw_token)
                # logger.info("JWT Authentication - Token validated successfully")
                # logger.info(f"JWT Authentication - Validated token: {validated_token}")
            except Exception as e:
                logger.error(f"JWT Authentication - Token validation failed: {str(e)}")
                raise InvalidToken(str(e))
            
            # Get the user from the validated token
            try:
                user = self.get_user(validated_token)
                logger.info(f"JWT Authentication - User obtained: {getattr(user, 'username', 'unknown')}")
                # logger.info(f"JWT Authentication - User attributes: {dir(user)}")
                
                # Ensure user has authentication attributes properly set
                self._add_auth_methods(user)
                
                # ต้องเพิ่มคำสั่งนี้เพื่อให้ is_authenticated ถูกเซ็ตค่าอย่างถูกต้อง
                if request:
                    # Store important attributes as special properties on the request
                    request._jwt_authenticated_user = user
                    request._jwt_auth_token = validated_token
                    request._jwt_user_authenticated = True
                    
                    # Set the user directly on the request
                    request.user = user
                    logger.info(f"JWT Authentication - Manually set request.user to authenticated user")
                
                # ตรวจสอบการ authenticate
                logger.info(f"JWT Authentication - Final is_authenticated: {user.is_authenticated}")
                
                return user, validated_token
            except Exception as e:
                logger.error(f"JWT Authentication - User retrieval failed: {str(e)}")
                raise InvalidToken(str(e))
            
        except InvalidToken as e:
            logger.error(f"JWT Authentication - InvalidToken: {str(e)}")
            raise AuthenticationFailed({
                'status': 'error',
                'code': 'token_invalid',
                'detail': str(e),
            }, code=status.HTTP_401_UNAUTHORIZED)
            
        except Exception as e:
            logger.error(f"JWT Authentication - Unexpected error: {str(e)}")
            raise AuthenticationFailed({
                'status': 'error',
                'code': 'authentication_failed',
                'detail': str(e),
            }, code=status.HTTP_401_UNAUTHORIZED)
    
    def get_raw_token(self, header):
        """
        Extract the raw token from the authorization header.
        """
        parts = header.decode().split()
        
        if len(parts) == 0:
            return None
            
        if parts[0] not in settings.SIMPLE_JWT.get('AUTH_HEADER_TYPES', ('Bearer',)):
            return None
            
        if len(parts) != 2:
            raise InvalidToken({
                'status': 'error',
                'code': 'invalid_header',
                'detail': _('Invalid authorization header format. Expected format: "Bearer token"'),
            })
            
        return parts[1]
    
    def get_validated_token(self, raw_token):
        """
        Validates an encoded JSON web token and returns a validated token
        wrapper object. Also checks expiration and blacklist.
        """
        # ตรวจสอบ token expiration ก่อน
        self.check_token_expiration(raw_token)

        # ตรวจสอบ blacklist ก่อน validate token
        if "rest_framework_simplejwt.token_blacklist" in settings.INSTALLED_APPS:
            self.check_token_blacklist(raw_token)

        # ต่อด้วยการ validate ตามปกติ
        return super().get_validated_token(raw_token)

    def check_token_expiration(self, raw_token):
        """
        ตรวจสอบว่า token หมดอายุหรือไม่ และทำ automatic logout
        """
        try:
            raw_token_str = str(raw_token)
            is_expired, expiry_time, error = TokenExpirationService.is_token_expired(raw_token_str)

            if error:
                logger.error(f"JWT Authentication - Token validation error: {error}")
                raise InvalidToken(_(f'Token validation failed: {error}'))

            if is_expired:
                logger.warning(f"JWT Authentication - Token expired at {expiry_time}")
                # Perform automatic logout for expired token
                AutomaticLogoutService.handle_expired_token_request(raw_token_str)
                raise InvalidToken(_('Token has expired'))

        except InvalidToken:
            # Re-raise InvalidToken exceptions
            raise
        except Exception as e:
            logger.error(f"JWT Authentication - Error checking token expiration: {str(e)}")
            # Don't fail authentication for expiration check errors
            pass

    def check_token_blacklist(self, raw_token):
        """
        ตรวจสอบว่า token อยู่ใน blacklist หรือไม่
        """
        try:
            from rest_framework_simplejwt.tokens import UntypedToken
            from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken
            from rest_framework_simplejwt.settings import api_settings
            
            # ถอดรหัส token เพื่อดู jti
            untyped_token = UntypedToken(raw_token)
            jti = untyped_token[api_settings.JTI_CLAIM]
            
            # แปลง jti hex string เป็นรูปแบบ UUID เดียวกันกับที่ใช้ใน revoke_token
            if len(jti) == 32 and all(c in '0123456789abcdef' for c in jti.lower()):
                # แปลง hex string เป็น UUID format
                formatted_jti = f"{jti[:8]}-{jti[8:12]}-{jti[12:16]}-{jti[16:20]}-{jti[20:]}"
                # logger.info(f"JWT Authentication - Converted hex jti {jti} to UUID format {formatted_jti}")
                jti = formatted_jti
            
            # logger.info(f"JWT Authentication - Checking blacklist for jti: {jti}")
            
            # ตรวจสอบว่า token อยู่ใน blacklist หรือไม่
            # ใช้ direct SQL query และเปรียบเทียบ hash ของ token
            from django.db import connection
            from authentication.utils import get_token_storage_hash

            # Hash the incoming token for comparison
            token_hash = get_token_storage_hash(str(raw_token))

            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM tcd_blacklisted_tokens bt
                    INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                    WHERE ot.jti = CONVERT(UNIQUEIDENTIFIER, %s) AND ot.token = %s
                """, [jti, token_hash])

                result = cursor.fetchone()
                is_blacklisted = result[0] > 0 if result else False
            
            if is_blacklisted:
                logger.error(f"JWT Authentication - Token is blacklisted: {jti}")
                raise InvalidToken(_('Token is blacklisted'))
            
            logger.info(f"JWT Authentication - Token not in blacklist: {jti}")
            
        except InvalidToken:
            # Re-raise InvalidToken exceptions (don't catch them)
            raise
        except Exception as e:
            # Only catch other exceptions (database errors, etc.)
            logger.error(f"JWT Authentication - Error checking blacklist: {str(e)}")
            # ถ้าเกิดข้อผิดพลาดในการตรวจสอบ blacklist ให้ถือว่า token ใช้ได้
            pass 