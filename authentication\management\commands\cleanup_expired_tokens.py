"""
Django Management Command for Token Cleanup

This command cleans up expired JWT tokens and performs maintenance
on the token tables to optimize storage and performance.
"""

import logging
from datetime import timedelta
from django.core.management.base import BaseCommand, CommandError
from django.db import connection, transaction
from django.utils import timezone
from django.conf import settings

from authentication.services.token_expiration_service import TokenExpirationService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up expired JWT tokens and perform token table maintenance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to keep expired tokens (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
        parser.add_argument(
            '--auto-logout',
            action='store_true',
            help='Perform automatic logout for expired tokens'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        """
        Main command handler
        """
        days_to_keep = options['days']
        dry_run = options['dry_run']
        auto_logout = options['auto_logout']
        verbose = options['verbose']

        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Starting token cleanup (keep {days_to_keep} days)')
            )

        try:
            # Step 1: Auto-logout expired tokens if requested
            if auto_logout:
                logout_stats = self.perform_auto_logout(dry_run, verbose)
                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Auto-logout: {logout_stats["tokens_processed"]} tokens processed, '
                            f'{logout_stats["users_logged_out"]} users logged out'
                        )
                    )

            # Step 2: Clean up expired tokens
            cleanup_stats = self.cleanup_expired_tokens(days_to_keep, dry_run, verbose)

            # Step 3: Clean up old blacklisted tokens
            blacklist_stats = self.cleanup_old_blacklisted_tokens(days_to_keep, dry_run, verbose)

            # Step 4: Optimize token tables
            if not dry_run:
                self.optimize_token_tables(verbose)

            # Summary
            self.stdout.write(
                self.style.SUCCESS(
                    f'\nToken cleanup completed:\n'
                    f'- Expired tokens cleaned: {cleanup_stats["deleted_count"]}\n'
                    f'- Blacklisted tokens cleaned: {blacklist_stats["deleted_count"]}\n'
                    f'- Total space optimized: {cleanup_stats["deleted_count"] + blacklist_stats["deleted_count"]} records'
                )
            )

        except Exception as e:
            logger.error(f'Token cleanup failed: {str(e)}')
            raise CommandError(f'Token cleanup failed: {str(e)}')

    def perform_auto_logout(self, dry_run=False, verbose=False):
        """
        Perform automatic logout for expired tokens
        """
        stats = {
            'tokens_processed': 0,
            'users_logged_out': 0
        }

        try:
            if verbose:
                self.stdout.write('Performing automatic logout for expired tokens...')

            # Use the existing cleanup method from TokenExpirationService
            result = TokenExpirationService.cleanup_expired_tokens()

            if result.get('success'):
                stats['tokens_processed'] = result.get('expired_tokens_found', 0)
                stats['users_logged_out'] = result.get('tokens_blacklisted', 0)

                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Auto-logout completed: {stats["users_logged_out"]} users logged out'
                        )
                    )
            else:
                if verbose:
                    self.stdout.write(
                        self.style.WARNING(f'Auto-logout failed: {result.get("error", "Unknown error")}')
                    )

        except Exception as e:
            logger.error(f'Auto-logout failed: {str(e)}')
            if verbose:
                self.stdout.write(
                    self.style.ERROR(f'Auto-logout failed: {str(e)}')
                )

        return stats

    def cleanup_expired_tokens(self, days_to_keep, dry_run=False, verbose=False):
        """
        Clean up expired outstanding tokens
        """
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        stats = {'deleted_count': 0}

        try:
            with connection.cursor() as cursor:
                if dry_run:
                    # Count what would be deleted
                    cursor.execute("""
                        SELECT COUNT(*)
                        FROM tcd_outstanding_tokens
                        WHERE expires_at < %s
                    """, [cutoff_date])
                    
                    count = cursor.fetchone()[0]
                    stats['deleted_count'] = count
                    
                    if verbose:
                        self.stdout.write(
                            f'Would delete {count} expired outstanding tokens older than {cutoff_date}'
                        )
                else:
                    # Actually delete expired tokens
                    cursor.execute("""
                        DELETE FROM tcd_outstanding_tokens
                        WHERE expires_at < %s
                    """, [cutoff_date])
                    
                    stats['deleted_count'] = cursor.rowcount
                    
                    if verbose:
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Deleted {stats["deleted_count"]} expired outstanding tokens'
                            )
                        )

        except Exception as e:
            logger.error(f'Failed to cleanup expired tokens: {str(e)}')
            raise

        return stats

    def cleanup_old_blacklisted_tokens(self, days_to_keep, dry_run=False, verbose=False):
        """
        Clean up old blacklisted tokens
        """
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        stats = {'deleted_count': 0}

        try:
            with connection.cursor() as cursor:
                if dry_run:
                    # Count what would be deleted
                    cursor.execute("""
                        SELECT COUNT(*)
                        FROM tcd_blacklisted_tokens bt
                        INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                        WHERE ot.expires_at < %s
                    """, [cutoff_date])
                    
                    count = cursor.fetchone()[0]
                    stats['deleted_count'] = count
                    
                    if verbose:
                        self.stdout.write(
                            f'Would delete {count} old blacklisted tokens older than {cutoff_date}'
                        )
                else:
                    # Delete old blacklisted tokens
                    cursor.execute("""
                        DELETE bt
                        FROM tcd_blacklisted_tokens bt
                        INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                        WHERE ot.expires_at < %s
                    """, [cutoff_date])
                    
                    stats['deleted_count'] = cursor.rowcount
                    
                    if verbose:
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Deleted {stats["deleted_count"]} old blacklisted tokens'
                            )
                        )

        except Exception as e:
            logger.error(f'Failed to cleanup blacklisted tokens: {str(e)}')
            raise

        return stats

    def optimize_token_tables(self, verbose=False):
        """
        Optimize token tables for better performance
        """
        try:
            if verbose:
                self.stdout.write('Optimizing token tables...')

            with connection.cursor() as cursor:
                # Update table statistics for better query performance
                cursor.execute("UPDATE STATISTICS tcd_outstanding_tokens")
                cursor.execute("UPDATE STATISTICS tcd_blacklisted_tokens")
                
                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS('Token tables optimized')
                    )

        except Exception as e:
            logger.warning(f'Table optimization failed: {str(e)}')
            if verbose:
                self.stdout.write(
                    self.style.WARNING(f'Table optimization failed: {str(e)}')
                )

    def get_token_statistics(self):
        """
        Get current token statistics
        """
        stats = {
            'total_outstanding': 0,
            'total_blacklisted': 0,
            'expired_outstanding': 0
        }

        try:
            with connection.cursor() as cursor:
                # Count total outstanding tokens
                cursor.execute("SELECT COUNT(*) FROM tcd_outstanding_tokens")
                stats['total_outstanding'] = cursor.fetchone()[0]

                # Count total blacklisted tokens
                cursor.execute("SELECT COUNT(*) FROM tcd_blacklisted_tokens")
                stats['total_blacklisted'] = cursor.fetchone()[0]

                # Count expired outstanding tokens
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM tcd_outstanding_tokens
                    WHERE expires_at < %s
                """, [timezone.now()])
                stats['expired_outstanding'] = cursor.fetchone()[0]

        except Exception as e:
            logger.error(f'Failed to get token statistics: {str(e)}')

        return stats
