# Windows-Compatible Django Scheduler Guide

## 🎉 Problem Solved!

**django-crontab ไม่รองรับ Windows** เพราะใช้ `fcntl` module ที่มีเฉพาะใน Unix/Linux เท่านั้น

เราได้แก้ไขแล้วด้วย **APScheduler** ที่รองรับ Windows! ✅

## 🚀 การตั้งค่าที่เสร็จแล้ว

### 1. ติดตั้ง APScheduler
```bash
pip install APScheduler==3.10.4
```

### 2. การตั้งค่าใน settings.py
```python
# APScheduler Configuration for Windows-compatible scheduled tasks
SCHEDULER_CONFIG = {
    'apscheduler.jobstores.default': {
        'type': 'sqlalchemy',
        'url': 'sqlite:///jobs.sqlite'
    },
    'apscheduler.executors.default': {
        'class': 'apscheduler.executors.pool:ThreadPoolExecutor',
        'max_workers': '20'
    },
    'apscheduler.job_defaults.coalesce': 'false',
    'apscheduler.job_defaults.max_instances': '3',
    'apscheduler.timezone': 'Asia/Bangkok',
}

# Scheduled Jobs Configuration
SCHEDULED_JOBS = [
    {
        'id': 'token_cleanup',
        'func': 'authentication.services.token_cleanup_service:TokenCleanupService.run_midnight_cleanup',
        'trigger': 'cron',
        'hour': 0,
        'minute': 10,  # Run at 00:10 AM every day
        'name': 'Token Cleanup Job',
        'replace_existing': True,
    },
]
```

### 3. Scheduler เริ่มทำงานอัตโนมัติ
- เริ่มทำงานเมื่อ Django start up
- รันใน background thread
- ไม่รบกวนการทำงานของ Django

## 📋 คำสั่งที่ใช้ได้

### ตรวจสอบสถานะ Scheduler
```bash
python manage.py scheduler --action=status --verbose
```

### ดูรายการ Jobs ทั้งหมด
```bash
python manage.py scheduler --action=list --verbose
```

### ทดสอบการทำงาน
```bash
python manage.py scheduler --action=test --verbose
```

### เริ่ม/หยุด Scheduler (ถ้าจำเป็น)
```bash
python manage.py scheduler --action=start --verbose
python manage.py scheduler --action=stop --verbose
```

## ⏰ กำหนดการรัน

ปัจจุบันตั้งให้รันที่ **00:10 AM ทุกวัน** (เปลี่ยนจาก 00:00 เป็น 00:10 ตามที่คุณต้องการ)

### เปลี่ยนเวลารัน
แก้ไขใน `settings.py`:
```python
SCHEDULED_JOBS = [
    {
        'id': 'token_cleanup',
        'func': 'authentication.services.token_cleanup_service:TokenCleanupService.run_midnight_cleanup',
        'trigger': 'cron',
        'hour': 0,      # ชั่วโมง (0-23)
        'minute': 10,   # นาที (0-59)
        'second': 0,    # วินาที (0-59) - optional
        # 'day': 1,     # วันที่ (1-31) - optional
        # 'month': 1,   # เดือน (1-12) - optional
        # 'day_of_week': 0,  # วันในสัปดาห์ (0=Monday, 6=Sunday) - optional
        'name': 'Token Cleanup Job',
        'replace_existing': True,
    },
]
```

### ตัวอย่างกำหนดการอื่น ๆ
```python
# ทุก 6 ชั่วโมง
{'hour': '*/6', 'minute': 0}

# ทุกวันจันทร์ 2:00 AM
{'hour': 2, 'minute': 0, 'day_of_week': 0}

# ทุกวันที่ 1 ของเดือน 3:30 AM
{'hour': 3, 'minute': 30, 'day': 1}
```

## 📊 ผลการทดสอบ

จากการทดสอบล่าสุด:
- ✅ **Scheduler Status**: Running
- ✅ **Active Jobs**: 1 job (token_cleanup)
- ✅ **Next Run**: 2025-09-12 00:10:00+07:00
- ✅ **Token Statistics**: 
  - Total outstanding tokens: 228
  - Expired tokens: 35
  - Active tokens: 193
- ✅ **Manual Cleanup Test**: Successful (0 tokens deleted - เพราะยังไม่ถึงเวลา 30 วัน)

## 🔧 การทำงาน

### 1. เมื่อ Django เริ่มทำงาน
- APScheduler จะเริ่มทำงานอัตโนมัติ
- Job จะถูกเพิ่มเข้าไปใน scheduler
- แสดง log: "Authentication app: Scheduler started successfully"

### 2. เมื่อถึงเวลาที่กำหนด (00:10 AM)
- `TokenCleanupService.run_midnight_cleanup()` จะถูกเรียก
- ลบ tokens ที่หมดอายุเกิน 30 วัน
- บันทึก log ลงใน Django logging system

### 3. การตรวจสอบ
- ใช้ management commands เพื่อตรวจสอบสถานะ
- ดู logs ใน Django logging
- ตรวจสอบ database เพื่อดูผลการทำงาน

## 🛠️ Troubleshooting

### ถ้า Scheduler ไม่ทำงาน
1. ตรวจสอบว่า APScheduler ติดตั้งแล้ว: `pip list | grep APScheduler`
2. ตรวจสอบ Django logs สำหรับ error messages
3. รัน test command: `python manage.py scheduler --action=test`

### ถ้า Jobs ไม่รัน
1. ตรวจสอบเวลาระบบ: `python -c "from datetime import datetime; print(datetime.now())"`
2. ตรวจสอบ timezone setting ใน settings.py
3. ดู next run time: `python manage.py scheduler --action=status --verbose`

## 🎯 สรุป

ตอนนี้คุณมี **Windows-compatible scheduled task** ที่:
- ✅ รองรับ Windows (ไม่ต้องใช้ Unix cron)
- ✅ รันอัตโนมัติทุกวันที่ 00:10 AM
- ✅ ลบ expired tokens ที่เก่าเกิน 30 วัน
- ✅ มี management commands สำหรับจัดการ
- ✅ มี logging และ monitoring
- ✅ ทำงานเหมือน Java's `@Scheduled(cron = "0 10 0 * * *")`

**Next Run**: 2025-09-12 00:10:00+07:00 🕙
