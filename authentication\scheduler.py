"""
APScheduler Configuration for Windows-compatible Scheduled Tasks

This module provides Windows-compatible scheduled task functionality
using APScheduler instead of django-crontab (which doesn't work on Windows).
"""

import logging
import atexit
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from django.conf import settings

logger = logging.getLogger(__name__)


class DjangoScheduler:
    """
    Windows-compatible scheduler using APScheduler
    """
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
    
    def start(self):
        """
        Start the scheduler with configured jobs
        """
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        try:
            # Initialize scheduler
            self.scheduler = BackgroundScheduler()
            
            # Configure scheduler if config exists
            scheduler_config = getattr(settings, 'SCHEDULER_CONFIG', {})
            if scheduler_config:
                self.scheduler.configure(**scheduler_config)
            
            # Add scheduled jobs
            self._add_scheduled_jobs()
            
            # Start scheduler
            self.scheduler.start()
            self.is_running = True
            
            # Ensure scheduler shuts down when Django exits
            atexit.register(self.shutdown)
            
            logger.info("APScheduler started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {str(e)}")
            raise
    
    def shutdown(self):
        """
        Shutdown the scheduler
        """
        if self.scheduler and self.is_running:
            try:
                self.scheduler.shutdown()
                self.is_running = False
                logger.info("APScheduler shut down successfully")
            except Exception as e:
                logger.error(f"Error shutting down scheduler: {str(e)}")
    
    def _add_scheduled_jobs(self):
        """
        Add jobs from Django settings
        """
        scheduled_jobs = getattr(settings, 'SCHEDULED_JOBS', [])
        
        for job_config in scheduled_jobs:
            try:
                self._add_job(job_config)
            except Exception as e:
                logger.error(f"Failed to add job {job_config.get('id', 'unknown')}: {str(e)}")
    
    def _add_job(self, job_config):
        """
        Add a single job to the scheduler
        """
        job_id = job_config.get('id')
        func_path = job_config.get('func')
        trigger_type = job_config.get('trigger', 'cron')
        
        if not job_id or not func_path:
            raise ValueError("Job must have 'id' and 'func' specified")
        
        # Parse function path
        module_path, func_name = func_path.split(':')
        
        # Import the function
        try:
            module = __import__(module_path, fromlist=[func_name])
            
            # Handle class methods (e.g., ClassName.method_name)
            if '.' in func_name:
                class_name, method_name = func_name.split('.', 1)
                cls = getattr(module, class_name)
                func = getattr(cls, method_name)
            else:
                func = getattr(module, func_name)
                
        except (ImportError, AttributeError) as e:
            raise ImportError(f"Cannot import function {func_path}: {str(e)}")
        
        # Create trigger
        if trigger_type == 'cron':
            trigger = CronTrigger(
                hour=job_config.get('hour'),
                minute=job_config.get('minute'),
                second=job_config.get('second', 0),
                day=job_config.get('day'),
                month=job_config.get('month'),
                day_of_week=job_config.get('day_of_week'),
                timezone=job_config.get('timezone', 'Asia/Bangkok')
            )
        else:
            raise ValueError(f"Unsupported trigger type: {trigger_type}")
        
        # Add job to scheduler
        self.scheduler.add_job(
            func=func,
            trigger=trigger,
            id=job_id,
            name=job_config.get('name', job_id),
            replace_existing=job_config.get('replace_existing', True),
            max_instances=job_config.get('max_instances', 1)
        )
        
        logger.info(f"Added scheduled job: {job_id} - {job_config.get('name', job_id)}")
    
    def add_job_manually(self, func, trigger, job_id, name=None, **kwargs):
        """
        Manually add a job to the scheduler
        """
        if not self.scheduler:
            raise RuntimeError("Scheduler not started")
        
        self.scheduler.add_job(
            func=func,
            trigger=trigger,
            id=job_id,
            name=name or job_id,
            **kwargs
        )
        
        logger.info(f"Manually added job: {job_id}")
    
    def remove_job(self, job_id):
        """
        Remove a job from the scheduler
        """
        if not self.scheduler:
            raise RuntimeError("Scheduler not started")
        
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Removed job: {job_id}")
        except Exception as e:
            logger.error(f"Failed to remove job {job_id}: {str(e)}")
    
    def list_jobs(self):
        """
        List all scheduled jobs
        """
        if not self.scheduler:
            return []
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time,
                'trigger': str(job.trigger)
            })
        
        return jobs
    
    def get_job_info(self, job_id):
        """
        Get information about a specific job
        """
        if not self.scheduler:
            return None
        
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time,
                    'trigger': str(job.trigger),
                    'func': str(job.func)
                }
        except Exception as e:
            logger.error(f"Failed to get job info for {job_id}: {str(e)}")
        
        return None


# Global scheduler instance
scheduler_instance = DjangoScheduler()


def start_scheduler():
    """
    Start the global scheduler instance
    """
    scheduler_instance.start()


def shutdown_scheduler():
    """
    Shutdown the global scheduler instance
    """
    scheduler_instance.shutdown()


def get_scheduler():
    """
    Get the global scheduler instance
    """
    return scheduler_instance
