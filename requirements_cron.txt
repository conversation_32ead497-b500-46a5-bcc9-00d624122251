# APScheduler for Windows-compatible Scheduled Tasks
# This replaces django-crontab which doesn't work on Windows

APScheduler==3.10.4

# Alternative scheduling packages (if needed):
# celery==5.3.4                    # For Celery Beat (requires Redis/RabbitMQ)
# django-apscheduler==0.6.2        # For Django-specific APScheduler integration
# django-cron==1.3.1               # For django-cron (Unix only)
# django-background-tasks==1.2.5   # For background tasks

# Note: django-crontab doesn't work on Windows due to fcntl dependency
