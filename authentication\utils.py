import hashlib
import time
import logging
from django.conf import settings
from django.utils import timezone
from rest_framework import status
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from .constants import APIErrorCodes, APIErrorMessages, APIConfig
from MCDC.models import TcdSetting

# Setup logging
logger = logging.getLogger(__name__)


# ==================== JWT Token Hashing Utilities ====================

def hash_jwt_token(token_string):
    """
    Hash JWT token using SHA-256 for secure storage

    Args:
        token_string (str): The JWT token string to hash

    Returns:
        str: SHA-256 hash of the token
    """
    if not token_string:
        return None

    # Use SHA-256 for secure hashing
    return hashlib.sha256(token_string.encode('utf-8')).hexdigest()


def verify_token_hash(token_string, token_hash):
    """
    Verify if a token string matches the stored hash

    Args:
        token_string (str): The JWT token string to verify
        token_hash (str): The stored hash to compare against

    Returns:
        bool: True if token matches hash, False otherwise
    """
    if not token_string or not token_hash:
        return False

    computed_hash = hash_jwt_token(token_string)
    return computed_hash == token_hash


def get_token_storage_hash(token_string):
    """
    Get the hash that should be stored in database for a token
    This function provides a consistent interface for token hashing

    Args:
        token_string (str): The JWT token string

    Returns:
        str: Hash suitable for database storage (first 64 characters)
    """
    if not token_string:
        return None

    full_hash = hash_jwt_token(token_string)
    # Store first 64 characters for database efficiency while maintaining security
    return full_hash[:64] if full_hash else None


def md5_hash(password):
    """
    เข้ารหัสรหัสผ่านด้วย MD5
    
    Args:
        password (str): รหัสผ่านที่ต้องการเข้ารหัส
        
    Returns:
        str: รหัสผ่านที่เข้ารหัสด้วย MD5 แล้ว
    """
    return hashlib.md5(password.encode('utf-8')).hexdigest()


def verify_password(plain_password, hashed_password):
    """
    ตรวจสอบรหัสผ่านโดยเปรียบเทียบกับ MD5 hash
    
    Args:
        plain_password (str): รหัสผ่านที่ยังไม่เข้ารหัส
        hashed_password (str): รหัสผ่านที่เข้ารหัสด้วย MD5 แล้ว
        
    Returns:
        bool: True ถ้ารหัสผ่านถูกต้อง, False ถ้าไม่ถูกต้อง
    """
    return md5_hash(plain_password) == hashed_password


# ==================== API Connection และ Validation Functions ====================

def validate_api_connection():
    """
    ตรวจสอบการเชื่อมต่อ API
    ตาม flowchart step: "ตรวจสอบการเชื่อมต่อ API"
    
    Returns:
        dict: ผลการตรวจสอบ {'success': bool, 'error': dict or None}
    """
    try:
        # Simulate API connection check
        # ในการใช้งานจริงจะเป็นการเรียก health check endpoint
        time.sleep(0.1)  # Simulate network delay
        
        # Mock connection check (จะต้องปรับให้เป็น actual API call)
        connection_success = True  # This should be actual connection test
        
        if connection_success:
            logger.info("API connection successful")
            return {'success': True, 'error': None}
        else:
            logger.error("API connection failed")
            return {
                'success': False,
                'error': {
                    'code': APIErrorCodes.CONNECTION_FAILED,
                    'message': APIErrorMessages.CONNECTION_FAILED
                }
            }
            
    except Exception as e:
        logger.error(f"API connection error: {str(e)}")
        return {
            'success': False,
            'error': {
                'code': APIErrorCodes.CONNECTION_TIMEOUT,
                'message': APIErrorMessages.CONNECTION_TIMEOUT
            }
        }


def validate_api_key(request):
    """
    ตรวจสอบ API Key
    ตาม flowchart step: "ตรวจสอบ API Key"

    Args:
        request: Django request object (can be None)

    Returns:
        dict: ผลการตรวจสอบ {'success': bool, 'error': dict or None}
    """
    try:
        # Handle None request
        if request is None:
            logger.error("Request is None, cannot validate API key")
            return {
                'success': False,
                'error': {
                    'code': APIErrorCodes.API_KEY_MISSING,
                    'message': APIErrorMessages.API_KEY_MISSING
                }
            }

        # ดึง API key จาก header
        api_key = request.headers.get('X-API-Key') or request.headers.get('Authorization')
        
        if not api_key:
            logger.error("API key missing in request")
            return {
                'success': False,
                'error': {
                    'code': APIErrorCodes.API_KEY_MISSING,
                    'message': APIErrorMessages.API_KEY_MISSING
                }
            }
        
        # ตรวจสอบ API key (ในที่นี้จะใช้ค่าจาก settings)
        # ในการใช้งานจริงอาจจะตรวจสอบจาก database หรือ external service
        valid_api_keys = getattr(settings, 'VALID_API_KEYS', ['mcdc-api-key-2024'])
        
        if api_key.replace('Bearer ', '').replace('ApiKey ', '') in valid_api_keys:
            logger.info("API key validation successful")
            return {'success': True, 'error': None}
        else:
            logger.error(f"Invalid API key: {api_key[:10]}...")
            return {
                'success': False,
                'error': {
                    'code': APIErrorCodes.INVALID_API_KEY,
                    'message': APIErrorMessages.INVALID_API_KEY
                }
            }
            
    except Exception as e:
        logger.error(f"API key validation error: {str(e)}")
        return {
            'success': False,
            'error': {
                'code': APIErrorCodes.INVALID_API_KEY,
                'message': APIErrorMessages.INVALID_API_KEY
            }
        }


def save_login_data(user_data, login_type='member'):
    """
    บันทึกข้อมูลการเข้าสู่ระบบ
    ตาม flowchart step: "บันทึกข้อมูลการเข้าสู่ระบบ"
    
    Args:
        user_data (dict): ข้อมูลผู้ใช้
        login_type (str): ประเภทการ login ('member' หรือ 'consultant')
        
    Returns:
        dict: ผลการบันทึก {'success': bool, 'error': dict or None}
    """
    try:
        # ในการใช้งานจริงจะบันทึกลง database หรือ external service
        logger.info(f"Saving login data for {login_type}: {user_data.get('username', 'unknown')}")
        
        # Mock save operation
        save_success = True  # This should be actual save operation
        
        if save_success:
            logger.info("Login data saved successfully")
            return {'success': True, 'error': None}
        else:
            logger.error("Failed to save login data")
            return {
                'success': False,
                'error': {
                    'code': APIErrorCodes.LOGIN_DATA_SAVE_FAILED,
                    'message': APIErrorMessages.LOGIN_DATA_SAVE_FAILED
                }
            }
            
    except Exception as e:
        logger.error(f"Save login data error: {str(e)}")
        return {
            'success': False,
            'error': {
                'code': APIErrorCodes.LOGIN_DATA_SAVE_FAILED,
                'message': APIErrorMessages.LOGIN_DATA_SAVE_FAILED
            }
        }


def send_user_data(user_data, tokens):
    """
    ส่งข้อมูลผู้ใช้งาน
    ตาม flowchart step: "ส่งข้อมูลผู้ใช้งาน"
    
    Args:
        user_data (dict): ข้อมูลผู้ใช้
        tokens (dict): JWT tokens
        
    Returns:
        dict: ผลการส่งข้อมูล {'success': bool, 'data': dict or None, 'error': dict or None}
    """
    try:
        logger.info(f"Sending user data for: {user_data.get('username', 'unknown')}")
        
        # ในการใช้งานจริงจะส่งข้อมูลไปยัง external service หรือ process อื่น
        # Mock send operation
        send_success = True  # This should be actual send operation
        
        if send_success:
            response_data = {
                'user': user_data,
                'tokens': tokens,
                'timestamp': timezone.now().isoformat()
            }
            logger.info("User data sent successfully")
            return {'success': True, 'data': response_data, 'error': None}
        else:
            logger.error("Failed to send user data")
            return {
                'success': False,
                'data': None,
                'error': {
                    'code': APIErrorCodes.USER_DATA_SEND_FAILED,
                    'message': APIErrorMessages.USER_DATA_SEND_FAILED
                }
            }
            
    except Exception as e:
        logger.error(f"Send user data error: {str(e)}")
        return {
            'success': False,
            'data': None,
            'error': {
                'code': APIErrorCodes.USER_DATA_SEND_FAILED,
                'message': APIErrorMessages.USER_DATA_SEND_FAILED
            }
        }


def validate_data_transmission(data):
    """
    ตรวจสอบการส่งข้อมูล
    ตาม flowchart step: "ตรวจสอบการส่งข้อมูล"
    
    Args:
        data (dict): ข้อมูลที่ส่ง
        
    Returns:
        dict: ผลการตรวจสอบ {'success': bool, 'error': dict or None}
    """
    try:
        # ตรวจสอบความสมบูรณ์ของข้อมูล
        required_fields = ['user', 'tokens', 'timestamp']
        
        for field in required_fields:
            if field not in data:
                logger.error(f"Missing required field: {field}")
                return {
                    'success': False,
                    'error': {
                        'code': APIErrorCodes.DATA_TRANSMISSION_FAILED,
                        'message': APIErrorMessages.DATA_TRANSMISSION_FAILED
                    }
                }
        
        logger.info("Data transmission validation successful")
        return {'success': True, 'error': None}
        
    except Exception as e:
        logger.error(f"Data transmission validation error: {str(e)}")
        return {
            'success': False,
            'error': {
                'code': APIErrorCodes.DATA_TRANSMISSION_FAILED,
                'message': APIErrorMessages.DATA_TRANSMISSION_FAILED
            }
        }


def prepare_user_display_data(user_data, tokens):
    """
    แสดงข้อมูลผู้ใช้งาน
    ตาม flowchart step: "แสดงข้อมูลผู้ใช้งาน"
    
    Args:
        user_data (dict): ข้อมูลผู้ใช้
        tokens (dict): JWT tokens
        
    Returns:
        dict: ผลการเตรียมข้อมูล {'success': bool, 'display_data': dict or None, 'error': dict or None}
    """
    try:
        logger.info(f"Preparing display data for: {user_data.get('username', 'unknown')}")
        
        # เตรียมข้อมูลสำหรับแสดงผล
        display_data = {
            'status': 'success',
            'message': 'เข้าสู่ระบบสำเร็จ',
            'data': {
                'user': user_data,
                'tokens': tokens
            }
        }
        
        logger.info("User display data prepared successfully")
        return {'success': True, 'display_data': display_data, 'error': None}
        
    except Exception as e:
        logger.error(f"Prepare user display data error: {str(e)}")
        return {
            'success': False,
            'display_data': None,
            'error': {
                'code': APIErrorCodes.USER_DISPLAY_FAILED,
                'message': APIErrorMessages.USER_DISPLAY_FAILED
            }
        }


def save_login_record(user_id, username, user_type, ip_address=None):
    """
    บันทึกเข้าสู่ระบบ (Login Record)
    ตาม flowchart step: "บันทึกเข้าสู่ระบบ"
    
    Args:
        user_id (int): ID ของผู้ใช้
        username (str): ชื่อผู้ใช้
        user_type (str): ประเภทผู้ใช้ ('member' หรือ 'consultant')
        ip_address (str): IP address (optional)
        
    Returns:
        dict: ผลการบันทึก {'success': bool, 'error': dict or None}
    """
    try:
        logger.info(f"Saving login record for {user_type}: {username}")
        
        # ในการใช้งานจริงจะบันทึกลง login_logs table
        login_record = {
            'user_id': user_id,
            'username': username,
            'user_type': user_type,
            'login_time': timezone.now().isoformat(),
            'ip_address': ip_address,
            'status': 'success'
        }
        
        # Mock save operation
        save_success = True  # This should be actual save to database
        
        if save_success:
            logger.info("Login record saved successfully")
            return {'success': True, 'error': None}
        else:
            logger.error("Failed to save login record")
            return {
                'success': False,
                'error': {
                    'code': APIErrorCodes.LOGIN_RECORD_SAVE_FAILED,
                    'message': APIErrorMessages.LOGIN_RECORD_SAVE_FAILED
                }
            }
            
    except Exception as e:
        logger.error(f"Save login record error: {str(e)}")
        return {
            'success': False,
            'error': {
                'code': APIErrorCodes.LOGIN_RECORD_SAVE_FAILED,
                'message': APIErrorMessages.LOGIN_RECORD_SAVE_FAILED
            }
        }


def get_client_ip(request):
    """
    ดึง IP address ของ client จาก request

    Args:
        request: Django request object (can be None)

    Returns:
        str: IP address or None if request is None
    """
    if request is None:
        return None

    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        # ดึงค่าจาก REMOTE_ADDR
        ip = request.META.get('REMOTE_ADDR')

    ip_split = ip.split(':')
    logger.info(f"Client IP: {ip_split}")
    if ip and len(ip_split) > 0 and len(ip_split) <= 2:
        # แยก IP ออกจาก Port
        ip = ip_split[0]
    else:
        ip = None

    return ip


def send_otp_email(recipient_email, otp_code, ref_code, purpose='register', language='th'):
    """
    ส่ง OTP ทางอีเมลโดยใช้ SMTP Server
    
    Args:
        recipient_email (str): อีเมลผู้รับ
        otp_code (str): รหัส OTP
        purpose (str): วัตถุประสงค์ของ OTP
        language (str): ภาษา (th/en)
        
    Returns:
        dict: ผลการส่งอีเมล {'success': bool, 'error': str or None}
    """
    try:
        # SMTP Configuration
        smtp_server = os.environ.get('SMTP_SERVER', 'smtp.gmail.com')
        smtp_port = os.environ.get('SMTP_PORT', 587)
        smtp_username = os.environ.get('SMTP_USERNAME', '')
        smtp_password = os.environ.get('SMTP_PASSWORD', '')
        
        # Email content
        message = MIMEMultipart()
        message['From'] = smtp_username
        message['To'] = recipient_email
        
        # ตั้งค่าหัวเรื่อง (subject) ของอีเมล
        purpose_th = {
            'login': 'เข้าสู่ระบบ',
            'register': 'ลงทะเบียน',
            'reset_password': 'รีเซ็ตรหัสผ่าน',
            'verify_email': 'ยืนยันอีเมล',
            'verify_phone': 'ยืนยันเบอร์โทรศัพท์'
        }.get(purpose, purpose)
        
        # กำหนดหัวเรื่องตามภาษา
        message['Subject'] = f"รหัส OTP สำหรับ{purpose_th}"
        
        email_setting = TcdSetting.objects.get(id=1)
        tel = email_setting.tel
        name = email_setting.name_th
        
        # สร้างเนื้อหาอีเมลตามรูปแบบที่กำหนด
        header = "ยินดีต้อนรับเข้าสู่ระบบศูนย์ข้อมูลที่ปรึกษา <br/><br/>"
        content = f"รหัสเพื่อใช้ยืนยันตัวตนในใช้งานระบบของท่านคือ {otp_code} หมายเลขอ้างอิง {ref_code}<br/>"
        footer = f"""<br/>
        อีเมลฉบับนี้เป็นการส่งโดยระบบอัตโนมัติ กรุณาอย่าตอบกลับ หากท่านต้องการสอบถามข้อมูลเพิ่มเติม กรุณาติดต่อฝ่ายบริการ โทร. {tel}
        <br/>
        <br/>
        ขอแสดงความนับถือ
        <br/>
        {name}<br/>
        """
        
        # รวมเนื้อหาทั้งหมด
        email_body = f"""
        <html>
        <body>
            <!--header-->
            {header}
            <!--content-->
            {content}
            <!--footer-->
            {footer}
        </body>
        </html>
        """
        
        # Attach HTML content
        message.attach(MIMEText(email_body, 'html'))
        
        # Log the email being sent (without showing the full OTP)
        masked_otp = otp_code[:2] + '*' * (len(otp_code) - 2)
        masked_ref_code = ref_code[:2] + '*' * (len(ref_code) - 2)
        logger.info(f"Sending OTP email to {recipient_email}, purpose: {purpose}, OTP: {masked_otp}, REF_CODE: {masked_ref_code}")
        
        # Create SMTP session
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()  # Enable TLS encryption
        server.login(smtp_username, smtp_password)
        
        # Send email
        server.send_message(message)
        server.quit()
        
        logger.info(f"Successfully sent OTP email to {recipient_email}")
        return {'success': True, 'error': None}
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"Failed to send OTP email: {error_message}")
        return {'success': False, 'error': error_message} 