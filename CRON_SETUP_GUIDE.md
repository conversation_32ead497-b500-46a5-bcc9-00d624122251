# Django Cron Jobs Setup Guide

## Overview
This guide explains how to set up scheduled tasks (cron jobs) for automatic token cleanup in the Django MCDC project, similar to Java's `@Scheduled` annotation.

## 🚀 Quick Setup

### 1. Install django-crontab
```bash
pip install django-crontab==0.7.1
```

### 2. Configuration Already Added
The following configuration has been added to `settings.py`:

```python
INSTALLED_APPS = [
    # ... other apps
    'django_crontab',  # Added for scheduled tasks
]

# Cron job configuration
CRONJOBS = [
    # Run token cleanup every midnight (0:00 AM)
    ('0 0 * * *', 'authentication.services.token_cleanup_service.TokenCleanupService.run_midnight_cleanup', '>> /tmp/token_cleanup.log 2>&1'),
]
```

### 3. Add Cron Jobs to System
```bash
# Add cron jobs to system crontab
python manage.py crontab add

# Verify cron jobs were added
crontab -l
```

## 📋 Available Commands

### Management Commands
```bash
# Set up and manage cron jobs
python manage.py setup_cron --action=show     # Show current configuration
python manage.py setup_cron --action=add      # Add cron jobs to system
python manage.py setup_cron --action=remove   # Remove cron jobs from system
python manage.py setup_cron --action=test     # Test cron job functions

# Manual token cleanup (for testing)
python manage.py cleanup_expired_tokens --verbose
python manage.py cleanup_expired_tokens --dry-run  # See what would be deleted
```

### Django-Crontab Commands
```bash
# Add cron jobs to system
python manage.py crontab add

# Remove cron jobs from system
python manage.py crontab remove

# Show configured cron jobs
python manage.py crontab show
```

## ⏰ Cron Schedule Examples

The current configuration runs cleanup at midnight every day. You can modify `CRONJOBS` in `settings.py`:

```python
CRONJOBS = [
    # Every midnight (0:00 AM)
    ('0 0 * * *', 'authentication.services.token_cleanup_service.TokenCleanupService.run_midnight_cleanup'),
    
    # Every 6 hours
    ('0 */6 * * *', 'authentication.services.token_cleanup_service.TokenCleanupService.run_midnight_cleanup'),
    
    # Every Sunday at 2:00 AM
    ('0 2 * * 0', 'authentication.services.token_cleanup_service.TokenCleanupService.run_midnight_cleanup'),
    
    # Every day at 3:30 AM
    ('30 3 * * *', 'authentication.services.token_cleanup_service.TokenCleanupService.run_midnight_cleanup'),
]
```

### Cron Schedule Format
```
* * * * *
│ │ │ │ │
│ │ │ │ └── Day of week (0-7, Sunday = 0 or 7)
│ │ │ └──── Month (1-12)
│ │ └────── Day of month (1-31)
│ └──────── Hour (0-23)
└────────── Minute (0-59)
```

## 🔧 Available Task Functions

### 1. Token Cleanup (Main Function)
```python
# In authentication/services/token_cleanup_service.py
TokenCleanupService.run_midnight_cleanup()
```

### 2. Task Wrapper Functions
```python
# In authentication/tasks.py
from authentication.tasks import (
    cleanup_expired_tokens,           # Main cleanup task
    auto_logout_expired_users,        # Auto-logout only
    get_token_health_report,          # Health monitoring
    comprehensive_token_maintenance   # All-in-one maintenance
)
```

## 📊 Monitoring and Logs

### Log Files
- Token cleanup logs: `/tmp/token_cleanup.log`
- Django logs: Check your Django logging configuration

### View Logs
```bash
# View token cleanup logs
tail -f /tmp/token_cleanup.log

# View recent cleanup activity
tail -n 50 /tmp/token_cleanup.log
```

### Health Monitoring
```python
# Get current token statistics
python manage.py shell
>>> from authentication.services.token_cleanup_service import TokenCleanupService
>>> stats = TokenCleanupService.get_token_statistics()
>>> print(stats)
```

## 🛠️ Troubleshooting

### Common Issues

1. **Cron jobs not running**
   ```bash
   # Check if cron service is running
   sudo service cron status
   
   # Check system crontab
   crontab -l
   
   # Check cron logs
   sudo tail -f /var/log/cron
   ```

2. **Permission issues**
   ```bash
   # Make sure Django project path is accessible
   # Update CRONTAB_COMMAND_PREFIX in settings.py
   CRONTAB_COMMAND_PREFIX = 'cd /full/path/to/your/project &&'
   ```

3. **Python path issues**
   ```bash
   # Add Python path to cron job
   CRONJOBS = [
       ('0 0 * * *', 'authentication.services.token_cleanup_service.TokenCleanupService.run_midnight_cleanup', '>> /tmp/token_cleanup.log 2>&1'),
   ]
   
   # Or set CRONTAB_PYTHON_EXECUTABLE
   CRONTAB_PYTHON_EXECUTABLE = '/path/to/your/python'
   ```

### Testing
```bash
# Test the cleanup function manually
python manage.py shell
>>> from authentication.services.token_cleanup_service import TokenCleanupService
>>> result = TokenCleanupService.run_midnight_cleanup()
>>> print(result)

# Test with management command
python manage.py setup_cron --action=test --verbose
```

## 🔄 Alternative Solutions

If django-crontab doesn't work for your environment:

### 1. System Crontab (Manual)
```bash
# Edit system crontab
crontab -e

# Add this line:
0 0 * * * cd /path/to/project && /path/to/python manage.py cleanup_expired_tokens >> /tmp/token_cleanup.log 2>&1
```

### 2. Celery Beat (Production)
```bash
pip install celery redis

# In settings.py
CELERY_BEAT_SCHEDULE = {
    'cleanup-expired-tokens': {
        'task': 'authentication.tasks.cleanup_expired_tokens',
        'schedule': crontab(hour=0, minute=0),
    },
}
```

### 3. APScheduler
```bash
pip install apscheduler django-apscheduler
```

## 📝 Summary

The setup provides:
- ✅ Automatic token cleanup every midnight
- ✅ Comprehensive logging and monitoring
- ✅ Easy management commands
- ✅ Flexible scheduling options
- ✅ Health reporting and statistics

This replaces the need for Java's `@Scheduled(cron = "0 0 0 * * *")` annotation in Django projects.
