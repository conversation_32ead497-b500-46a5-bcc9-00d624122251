"""
Authentication Tasks for Scheduled Jobs

This module contains task functions that can be called by cron jobs
or other scheduling systems for authentication-related maintenance.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from authentication.services.token_cleanup_service import TokenCleanupService
from authentication.services.token_expiration_service import TokenExpirationService

logger = logging.getLogger(__name__)


def cleanup_expired_tokens() -> Dict[str, Any]:
    """
    Task function for cleaning up expired tokens.
    This function can be called by cron jobs or other scheduling systems.
    
    Returns:
        Dict containing cleanup results and statistics
    """
    logger.info("Starting scheduled token cleanup task")
    
    try:
        # Run the comprehensive midnight cleanup
        result = TokenCleanupService.run_midnight_cleanup()
        
        # Log the results
        if result.get('success'):
            logger.info(
                f"Token cleanup task completed successfully. "
                f"Total deleted: {result.get('total_deleted', 0)} tokens"
            )
        else:
            logger.error(
                f"Token cleanup task failed: {result.get('errors', 'Unknown error')}"
            )
        
        return result
        
    except Exception as e:
        error_msg = f"Token cleanup task failed with exception: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return {
            'success': False,
            'error': error_msg,
            'start_time': datetime.now().isoformat(),
            'total_deleted': 0
        }


def auto_logout_expired_users() -> Dict[str, Any]:
    """
    Task function for automatically logging out users with expired tokens.
    This function can be called by cron jobs for security maintenance.
    
    Returns:
        Dict containing logout results and statistics
    """
    logger.info("Starting scheduled auto-logout task")
    
    try:
        # Run the auto-logout process
        result = TokenExpirationService.cleanup_expired_tokens()
        
        # Log the results
        if result.get('success'):
            logger.info(
                f"Auto-logout task completed successfully. "
                f"Users logged out: {result.get('tokens_blacklisted', 0)}"
            )
        else:
            logger.error(
                f"Auto-logout task failed: {result.get('error', 'Unknown error')}"
            )
        
        return result
        
    except Exception as e:
        error_msg = f"Auto-logout task failed with exception: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return {
            'success': False,
            'error': error_msg,
            'expired_tokens_found': 0,
            'tokens_blacklisted': 0
        }


def get_token_health_report() -> Dict[str, Any]:
    """
    Task function for generating token health reports.
    This function can be called by cron jobs for monitoring.
    
    Returns:
        Dict containing token statistics and health information
    """
    logger.info("Generating token health report")
    
    try:
        # Get current token statistics
        stats = TokenCleanupService.get_token_statistics()
        
        # Calculate health metrics
        total_tokens = stats.get('total_outstanding_tokens', 0)
        expired_tokens = stats.get('expired_outstanding_tokens', 0)
        active_tokens = stats.get('active_outstanding_tokens', 0)
        
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'success': True,
            'statistics': stats,
            'health_metrics': {
                'total_tokens': total_tokens,
                'active_tokens': active_tokens,
                'expired_tokens': expired_tokens,
                'expired_percentage': (expired_tokens / total_tokens * 100) if total_tokens > 0 else 0,
                'health_status': 'healthy' if expired_tokens < (total_tokens * 0.1) else 'needs_cleanup'
            }
        }
        
        logger.info(f"Token health report generated: {health_report['health_metrics']['health_status']}")
        return health_report
        
    except Exception as e:
        error_msg = f"Token health report generation failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return {
            'success': False,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }


def comprehensive_token_maintenance() -> Dict[str, Any]:
    """
    Comprehensive token maintenance task that combines multiple operations.
    This function performs auto-logout, cleanup, and health reporting.
    
    Returns:
        Dict containing results from all maintenance operations
    """
    logger.info("Starting comprehensive token maintenance")
    
    maintenance_results = {
        'start_time': datetime.now().isoformat(),
        'success': True,
        'operations': {}
    }
    
    try:
        # Step 1: Auto-logout expired users
        logout_result = auto_logout_expired_users()
        maintenance_results['operations']['auto_logout'] = logout_result
        
        # Step 2: Clean up expired tokens
        cleanup_result = cleanup_expired_tokens()
        maintenance_results['operations']['cleanup'] = cleanup_result
        
        # Step 3: Generate health report
        health_result = get_token_health_report()
        maintenance_results['operations']['health_report'] = health_result
        
        # Determine overall success
        all_successful = all([
            logout_result.get('success', False),
            cleanup_result.get('success', False),
            health_result.get('success', False)
        ])
        
        maintenance_results['success'] = all_successful
        maintenance_results['end_time'] = datetime.now().isoformat()
        
        if all_successful:
            logger.info("Comprehensive token maintenance completed successfully")
        else:
            logger.warning("Comprehensive token maintenance completed with some failures")
        
        return maintenance_results
        
    except Exception as e:
        error_msg = f"Comprehensive token maintenance failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        maintenance_results['success'] = False
        maintenance_results['error'] = error_msg
        maintenance_results['end_time'] = datetime.now().isoformat()
        
        return maintenance_results
