"""
Django Management Command for Setting Up Cron Jobs

This command helps set up and manage cron jobs for token cleanup
and other scheduled maintenance tasks.
"""

import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up and manage cron jobs for token cleanup and maintenance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['add', 'remove', 'show', 'test'],
            default='show',
            help='Action to perform: add, remove, show, or test cron jobs'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        """
        Main command handler
        """
        action = options['action']
        verbose = options['verbose']

        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Managing cron jobs - Action: {action}')
            )

        try:
            if action == 'add':
                self.add_cron_jobs(verbose)
            elif action == 'remove':
                self.remove_cron_jobs(verbose)
            elif action == 'show':
                self.show_cron_jobs(verbose)
            elif action == 'test':
                self.test_cron_jobs(verbose)

        except Exception as e:
            logger.error(f'Cron job management failed: {str(e)}')
            raise CommandError(f'Cron job management failed: {str(e)}')

    def add_cron_jobs(self, verbose=False):
        """
        Add cron jobs to the system
        """
        try:
            from django_crontab.management.commands.crontab import Command as CrontabCommand
            
            if verbose:
                self.stdout.write('Adding cron jobs to system...')
            
            # Create crontab command instance and add jobs
            crontab_cmd = CrontabCommand()
            crontab_cmd.handle(action='add')
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS('Cron jobs added successfully!')
                )
                self.stdout.write('You can verify by running: crontab -l')
                
        except ImportError:
            self.stdout.write(
                self.style.ERROR(
                    'django-crontab is not installed. Please install it first:\n'
                    'pip install django-crontab'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to add cron jobs: {str(e)}')
            )

    def remove_cron_jobs(self, verbose=False):
        """
        Remove cron jobs from the system
        """
        try:
            from django_crontab.management.commands.crontab import Command as CrontabCommand
            
            if verbose:
                self.stdout.write('Removing cron jobs from system...')
            
            # Create crontab command instance and remove jobs
            crontab_cmd = CrontabCommand()
            crontab_cmd.handle(action='remove')
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS('Cron jobs removed successfully!')
                )
                
        except ImportError:
            self.stdout.write(
                self.style.ERROR(
                    'django-crontab is not installed. Please install it first:\n'
                    'pip install django-crontab'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to remove cron jobs: {str(e)}')
            )

    def show_cron_jobs(self, verbose=False):
        """
        Show current cron job configuration
        """
        try:
            from django_crontab.management.commands.crontab import Command as CrontabCommand
            
            if verbose:
                self.stdout.write('Current cron job configuration:')
            
            # Show configured cron jobs from settings
            cronjobs = getattr(settings, 'CRONJOBS', [])
            
            if cronjobs:
                self.stdout.write(
                    self.style.SUCCESS(f'Found {len(cronjobs)} configured cron job(s):')
                )
                
                for i, job in enumerate(cronjobs, 1):
                    if len(job) >= 2:
                        schedule = job[0]
                        command = job[1]
                        log_file = job[2] if len(job) > 2 else 'No log file specified'
                        
                        self.stdout.write(f'\n{i}. Schedule: {schedule}')
                        self.stdout.write(f'   Command: {command}')
                        self.stdout.write(f'   Log: {log_file}')
            else:
                self.stdout.write(
                    self.style.WARNING('No cron jobs configured in settings.py')
                )
                
            # Show system cron jobs
            if verbose:
                self.stdout.write('\nTo see active system cron jobs, run: crontab -l')
                
        except ImportError:
            self.stdout.write(
                self.style.ERROR(
                    'django-crontab is not installed. Please install it first:\n'
                    'pip install django-crontab'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to show cron jobs: {str(e)}')
            )

    def test_cron_jobs(self, verbose=False):
        """
        Test cron job functions manually
        """
        if verbose:
            self.stdout.write('Testing cron job functions...')
        
        try:
            # Test token cleanup service
            from authentication.services.token_cleanup_service import TokenCleanupService
            
            self.stdout.write('\n1. Testing TokenCleanupService.get_token_statistics()...')
            stats = TokenCleanupService.get_token_statistics()
            self.stdout.write(f'   Result: {stats}')
            
            self.stdout.write('\n2. Testing TokenCleanupService.run_midnight_cleanup() (dry run)...')
            # Note: This will actually run the cleanup, so be careful in production
            self.stdout.write('   Warning: This will perform actual cleanup!')
            
            confirm = input('Do you want to proceed with actual cleanup? (y/N): ')
            if confirm.lower() == 'y':
                result = TokenCleanupService.run_midnight_cleanup()
                self.stdout.write(f'   Result: {result}')
            else:
                self.stdout.write('   Skipped actual cleanup test.')
            
            # Test task functions
            self.stdout.write('\n3. Testing authentication.tasks functions...')
            from authentication.tasks import get_token_health_report
            
            health_report = get_token_health_report()
            self.stdout.write(f'   Health Report: {health_report}')
            
            self.stdout.write(
                self.style.SUCCESS('\nCron job function tests completed!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Cron job function test failed: {str(e)}')
            )
