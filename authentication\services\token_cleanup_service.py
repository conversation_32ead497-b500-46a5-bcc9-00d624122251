"""
Token Cleanup Service for Cron Job

This service is designed to be run as a cron job every midnight to clean up
expired tokens from tcd_blacklisted_tokens and tcd_outstanding_tokens tables.
"""

import logging
from datetime import datetime, timedelta
from django.db import connection, transaction
from django.utils import timezone
from django.conf import settings
from typing import Dict, Any

logger = logging.getLogger(__name__)


class TokenCleanupService:
    """
    Service for cleaning up expired tokens from database tables.
    Designed to be run as a scheduled cron job.
    """
    
    @staticmethod
    def cleanup_expired_tokens(days_to_keep: int = 30) -> Dict[str, Any]:
        """
        Clean up expired tokens from both outstanding and blacklisted token tables.
        
        Args:
            days_to_keep (int): Number of days to keep expired tokens (default: 30)
            
        Returns:
            Dict containing cleanup statistics and results
        """
        start_time = datetime.now()
        logger.info(f"Starting token cleanup service at {start_time}")
        
        results = {
            'start_time': start_time.isoformat(),
            'success': False,
            'outstanding_tokens_deleted': 0,
            'blacklisted_tokens_deleted': 0,
            'total_deleted': 0,
            'errors': [],
            'execution_time_seconds': 0
        }
        
        try:
            # Calculate cutoff date
            cutoff_date = timezone.now() - timedelta(days=days_to_keep)
            logger.info(f"Cleaning up tokens older than {cutoff_date}")
            
            with transaction.atomic():
                # Step 1: Clean up blacklisted tokens first (due to foreign key constraint)
                blacklisted_result = TokenCleanupService._cleanup_blacklisted_tokens(cutoff_date)
                results['blacklisted_tokens_deleted'] = blacklisted_result['deleted_count']
                
                # Step 2: Clean up outstanding tokens
                outstanding_result = TokenCleanupService._cleanup_outstanding_tokens(cutoff_date)
                results['outstanding_tokens_deleted'] = outstanding_result['deleted_count']
                
                # Calculate totals
                results['total_deleted'] = results['outstanding_tokens_deleted'] + results['blacklisted_tokens_deleted']
                results['success'] = True
                
                logger.info(
                    f"Token cleanup completed successfully. "
                    f"Outstanding: {results['outstanding_tokens_deleted']}, "
                    f"Blacklisted: {results['blacklisted_tokens_deleted']}, "
                    f"Total: {results['total_deleted']}"
                )
                
        except Exception as e:
            error_msg = f"Token cleanup failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            results['errors'].append(error_msg)
            results['success'] = False
            
        finally:
            end_time = datetime.now()
            results['end_time'] = end_time.isoformat()
            results['execution_time_seconds'] = (end_time - start_time).total_seconds()
            
        return results
    
    @staticmethod
    def _cleanup_blacklisted_tokens(cutoff_date: datetime) -> Dict[str, Any]:
        """
        Clean up expired blacklisted tokens.
        
        Args:
            cutoff_date (datetime): Cutoff date for cleanup
            
        Returns:
            Dict containing cleanup results
        """
        logger.info("Cleaning up expired blacklisted tokens...")
        
        try:
            with connection.cursor() as cursor:
                # Delete blacklisted tokens where the associated outstanding token has expired
                cursor.execute("""
                    DELETE bt
                    FROM tcd_blacklisted_tokens bt
                    INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                    WHERE ot.expires_at < %s
                """, [cutoff_date])
                
                deleted_count = cursor.rowcount
                logger.info(f"Deleted {deleted_count} expired blacklisted tokens")
                
                return {
                    'success': True,
                    'deleted_count': deleted_count
                }
                
        except Exception as e:
            logger.error(f"Failed to cleanup blacklisted tokens: {str(e)}")
            raise
    
    @staticmethod
    def _cleanup_outstanding_tokens(cutoff_date: datetime) -> Dict[str, Any]:
        """
        Clean up expired outstanding tokens.
        
        Args:
            cutoff_date (datetime): Cutoff date for cleanup
            
        Returns:
            Dict containing cleanup results
        """
        logger.info("Cleaning up expired outstanding tokens...")
        
        try:
            with connection.cursor() as cursor:
                # Delete outstanding tokens that have expired
                cursor.execute("""
                    DELETE FROM tcd_outstanding_tokens
                    WHERE expires_at < %s
                """, [cutoff_date])
                
                deleted_count = cursor.rowcount
                logger.info(f"Deleted {deleted_count} expired outstanding tokens")
                
                return {
                    'success': True,
                    'deleted_count': deleted_count
                }
                
        except Exception as e:
            logger.error(f"Failed to cleanup outstanding tokens: {str(e)}")
            raise
    
    @staticmethod
    def get_token_statistics() -> Dict[str, Any]:
        """
        Get current token statistics for monitoring.
        
        Returns:
            Dict containing token statistics
        """
        try:
            with connection.cursor() as cursor:
                stats = {}
                
                # Count total outstanding tokens
                cursor.execute("SELECT COUNT(*) FROM tcd_outstanding_tokens")
                stats['total_outstanding_tokens'] = cursor.fetchone()[0]
                
                # Count expired outstanding tokens
                cursor.execute("""
                    SELECT COUNT(*) FROM tcd_outstanding_tokens 
                    WHERE expires_at < %s
                """, [timezone.now()])
                stats['expired_outstanding_tokens'] = cursor.fetchone()[0]
                
                # Count active outstanding tokens
                cursor.execute("""
                    SELECT COUNT(*) FROM tcd_outstanding_tokens 
                    WHERE expires_at >= %s
                """, [timezone.now()])
                stats['active_outstanding_tokens'] = cursor.fetchone()[0]
                
                # Count total blacklisted tokens
                cursor.execute("SELECT COUNT(*) FROM tcd_blacklisted_tokens")
                stats['total_blacklisted_tokens'] = cursor.fetchone()[0]
                
                # Count blacklisted tokens with expired outstanding tokens
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM tcd_blacklisted_tokens bt
                    INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                    WHERE ot.expires_at < %s
                """, [timezone.now()])
                stats['expired_blacklisted_tokens'] = cursor.fetchone()[0]
                
                logger.info(f"Token statistics: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get token statistics: {str(e)}")
            return {}
    
    @staticmethod
    def run_midnight_cleanup() -> Dict[str, Any]:
        """
        Main method to be called by cron job at midnight.
        Performs comprehensive token cleanup with logging.
        
        Returns:
            Dict containing cleanup results
        """
        logger.info("=" * 50)
        logger.info("MIDNIGHT TOKEN CLEANUP STARTED")
        logger.info("=" * 50)
        
        try:
            # Get statistics before cleanup
            stats_before = TokenCleanupService.get_token_statistics()
            logger.info(f"Statistics before cleanup: {stats_before}")
            
            # Perform cleanup (keep expired tokens for 30 days)
            cleanup_result = TokenCleanupService.cleanup_expired_tokens(days_to_keep=30)
            
            # Get statistics after cleanup
            stats_after = TokenCleanupService.get_token_statistics()
            logger.info(f"Statistics after cleanup: {stats_after}")
            
            # Add statistics to result
            cleanup_result['stats_before'] = stats_before
            cleanup_result['stats_after'] = stats_after
            
            if cleanup_result['success']:
                logger.info("MIDNIGHT TOKEN CLEANUP COMPLETED SUCCESSFULLY")
            else:
                logger.error("MIDNIGHT TOKEN CLEANUP FAILED")
                
        except Exception as e:
            logger.error(f"Midnight cleanup failed with exception: {str(e)}", exc_info=True)
            cleanup_result = {
                'success': False,
                'error': str(e),
                'start_time': datetime.now().isoformat()
            }
        
        logger.info("=" * 50)
        return cleanup_result
