"""
Token Expiration Detection and Automatic Logout Service

This service handles automatic detection of expired JWT tokens and performs
cleanup operations including automatic logout and session cleanup.
"""

import logging
import jwt
from datetime import datetime, timezone
from django.conf import settings
from django.db import connection, transaction
from django.utils import timezone as django_timezone

from authentication.models import TcdAppMember, TcdUserConsult
from authentication.utils import get_token_storage_hash
from utils.response import APIResponse

logger = logging.getLogger(__name__)


class TokenExpirationService:
    """
    Service for handling token expiration detection and automatic logout
    """
    
    @staticmethod
    def is_token_expired(token_string):
        """
        Check if a JWT token is expired
        
        Args:
            token_string (str): The JWT token string to check
            
        Returns:
            tuple: (is_expired: bool, expiry_time: datetime or None, error: str or None)
        """
        try:
            # Decode token without verification to get expiry time
            unverified_payload = jwt.decode(
                token_string,
                options={"verify_signature": False, "verify_exp": False}
            )
            
            exp_timestamp = unverified_payload.get('exp')
            if not exp_timestamp:
                return True, None, "Token has no expiration time"
            
            expiry_time = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
            current_time = datetime.now(timezone.utc)
            
            is_expired = current_time >= expiry_time
            
            logger.info(f"Token expiry check: expired={is_expired}, expiry={expiry_time}, current={current_time}")
            
            return is_expired, expiry_time, None
            
        except jwt.DecodeError as e:
            logger.error(f"Failed to decode token for expiry check: {str(e)}")
            return True, None, f"Invalid token format: {str(e)}"
        except Exception as e:
            logger.error(f"Error checking token expiration: {str(e)}")
            return True, None, f"Token expiration check failed: {str(e)}"
    
    @staticmethod
    def detect_and_handle_expired_token(token_string, language='th'):
        """
        Detect if token is expired and handle automatic logout if needed
        
        Args:
            token_string (str): The JWT token string to check
            language (str): Language for error messages
            
        Returns:
            dict: Response indicating if token is expired and action taken
        """
        try:
            is_expired, expiry_time, error = TokenExpirationService.is_token_expired(token_string)
            
            if error:
                logger.error(f"Token validation error: {error}")
                return APIResponse.error(
                    error_code=1005,  # Invalid token
                    data={'reason': error},
                    language=language,
                    status_code=401
                )
            
            if is_expired:
                logger.info(f"Token expired at {expiry_time}, performing automatic logout")
                
                # Perform automatic logout and cleanup
                cleanup_result = TokenExpirationService.perform_automatic_logout(token_string, language)
                
                return APIResponse.error(
                    error_code=1006,  # Token expired
                    data={
                        'reason': 'Token has expired',
                        'expired_at': expiry_time.isoformat() if expiry_time else None,
                        'auto_logout_performed': cleanup_result.get('success', False)
                    },
                    language=language,
                    status_code=401
                )
            
            # Token is still valid
            return {
                'success': True,
                'expired': False,
                'expiry_time': expiry_time.isoformat() if expiry_time else None
            }
            
        except Exception as e:
            logger.error(f"Error in token expiration detection: {str(e)}")
            return APIResponse.error(
                error_code=5000,  # Server error
                data={'reason': f'Token expiration check failed: {str(e)}'},
                language=language,
                status_code=500
            )
    
    @staticmethod
    def perform_automatic_logout(token_string, language='th'):
        """
        Perform automatic logout and cleanup for expired token
        
        Args:
            token_string (str): The expired JWT token string
            language (str): Language for messages
            
        Returns:
            dict: Result of logout operation
        """
        try:
            # Decode token to get user information
            unverified_payload = jwt.decode(
                token_string,
                options={"verify_signature": False, "verify_exp": False}
            )
            
            user_id = unverified_payload.get('user_id')
            user_type = unverified_payload.get('user_type')
            jti = unverified_payload.get('jti')
            
            if not user_id or not user_type:
                logger.error("Cannot perform automatic logout: missing user information in token")
                return {'success': False, 'reason': 'Missing user information'}
            
            logger.info(f"Performing automatic logout for {user_type} user {user_id}")
            
            # Clear user session data
            cleanup_success = TokenExpirationService.clear_user_session_data(user_id, user_type)
            
            # Blacklist the expired token
            blacklist_success = TokenExpirationService.blacklist_expired_token(token_string, jti, user_id, user_type)
            
            return {
                'success': cleanup_success and blacklist_success,
                'user_id': user_id,
                'user_type': user_type,
                'session_cleared': cleanup_success,
                'token_blacklisted': blacklist_success
            }
            
        except Exception as e:
            logger.error(f"Error performing automatic logout: {str(e)}")
            return {'success': False, 'reason': str(e)}
    
    @staticmethod
    def clear_user_session_data(user_id, user_type):
        """
        Clear all session data for a user (token_app, cached data, etc.)
        
        Args:
            user_id (int): User ID
            user_type (str): User type ('member' or 'consultant')
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Clearing session data for {user_type} user {user_id}")
            
            if user_type == 'member':
                TcdAppMember.objects.filter(id=user_id).update(token_app=None)
                logger.info(f"Cleared token_app for member {user_id}")
            elif user_type == 'consultant':
                TcdUserConsult.objects.filter(id=user_id).update(token_app=None)
                logger.info(f"Cleared token_app for consultant {user_id}")
            else:
                logger.warning(f"Unknown user type for session cleanup: {user_type}")
                return False
            
            # Additional cleanup can be added here (cache, etc.)
            
            return True
            
        except Exception as e:
            logger.error(f"Error clearing session data for {user_type} user {user_id}: {str(e)}")
            return False
    
    @staticmethod
    def blacklist_expired_token(token_string, jti, user_id, user_type):
        """
        Blacklist an expired token
        
        Args:
            token_string (str): The token string
            jti (str): Token JTI
            user_id (int): User ID
            user_type (str): User type
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from authentication.views import format_jti_for_database
            
            token_hash = get_token_storage_hash(token_string)
            jti_formatted = format_jti_for_database(jti)
            
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Find the outstanding token
                    cursor.execute("""
                        SELECT id FROM tcd_outstanding_tokens
                        WHERE jti = CONVERT(UNIQUEIDENTIFIER, %s) AND token = %s
                    """, [jti_formatted, token_hash])
                    
                    result = cursor.fetchone()
                    if not result:
                        logger.warning(f"Outstanding token not found for JTI: {jti_formatted}")
                        return False
                    
                    token_id = result[0]
                    
                    # Check if already blacklisted
                    cursor.execute("""
                        SELECT COUNT(*) FROM tcd_blacklisted_tokens WHERE token_id = %s
                    """, [token_id])
                    
                    if cursor.fetchone()[0] > 0:
                        logger.info(f"Token {jti_formatted} is already blacklisted")
                        return True
                    
                    # Blacklist the token
                    cursor.execute("""
                        INSERT INTO tcd_blacklisted_tokens (token_id, blacklisted_at)
                        VALUES (%s, %s)
                    """, [token_id, django_timezone.now()])
                    
                    logger.info(f"Successfully blacklisted expired token {jti_formatted}")
                    return True
                    
        except Exception as e:
            logger.error(f"Error blacklisting expired token: {str(e)}")
            return False
    
    @staticmethod
    def cleanup_expired_tokens():
        """
        Cleanup expired tokens from the database (for scheduled maintenance)
        
        Returns:
            dict: Cleanup statistics
        """
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Find expired tokens
                    cursor.execute("""
                        SELECT ot.id, ot.jti, ot.user_id, ot.user_type
                        FROM tcd_outstanding_tokens ot
                        WHERE ot.expires_at < %s
                        AND NOT EXISTS (
                            SELECT 1 FROM tcd_blacklisted_tokens bt WHERE bt.token_id = ot.id
                        )
                    """, [django_timezone.now()])
                    
                    expired_tokens = cursor.fetchall()
                    
                    blacklisted_count = 0
                    for token_id, jti, user_id, user_type in expired_tokens:
                        try:
                            # Blacklist expired token
                            cursor.execute("""
                                INSERT INTO tcd_blacklisted_tokens (token_id, blacklisted_at)
                                VALUES (%s, %s)
                            """, [token_id, django_timezone.now()])
                            
                            # Clear user session data
                            TokenExpirationService.clear_user_session_data(user_id, user_type)
                            
                            blacklisted_count += 1
                            logger.info(f"Auto-blacklisted expired token: {jti}")
                            
                        except Exception as e:
                            logger.error(f"Failed to blacklist expired token {jti}: {str(e)}")
                    
                    return {
                        'success': True,
                        'expired_tokens_found': len(expired_tokens),
                        'tokens_blacklisted': blacklisted_count
                    }
                    
        except Exception as e:
            logger.error(f"Error during expired token cleanup: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
