"""
Django Management Command for APScheduler Management

This command helps manage the APScheduler for Windows-compatible scheduled tasks.
"""

import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Manage APScheduler for Windows-compatible scheduled tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['start', 'stop', 'status', 'list', 'test'],
            default='status',
            help='Action to perform: start, stop, status, list, or test scheduler'
        )
        parser.add_argument(
            '--job-id',
            type=str,
            help='Specific job ID for job-specific actions'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        """
        Main command handler
        """
        action = options['action']
        job_id = options.get('job_id')
        verbose = options['verbose']

        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Managing APScheduler - Action: {action}')
            )

        try:
            if action == 'start':
                self.start_scheduler(verbose)
            elif action == 'stop':
                self.stop_scheduler(verbose)
            elif action == 'status':
                self.show_status(verbose)
            elif action == 'list':
                self.list_jobs(verbose)
            elif action == 'test':
                self.test_scheduler(job_id, verbose)

        except Exception as e:
            logger.error(f'Scheduler management failed: {str(e)}')
            raise CommandError(f'Scheduler management failed: {str(e)}')

    def start_scheduler(self, verbose=False):
        """
        Start the scheduler
        """
        try:
            from authentication.scheduler import get_scheduler
            
            scheduler = get_scheduler()
            
            if scheduler.is_running:
                self.stdout.write(
                    self.style.WARNING('Scheduler is already running')
                )
                return
            
            scheduler.start()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS('APScheduler started successfully!')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to start scheduler: {str(e)}')
            )

    def stop_scheduler(self, verbose=False):
        """
        Stop the scheduler
        """
        try:
            from authentication.scheduler import get_scheduler
            
            scheduler = get_scheduler()
            
            if not scheduler.is_running:
                self.stdout.write(
                    self.style.WARNING('Scheduler is not running')
                )
                return
            
            scheduler.shutdown()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS('APScheduler stopped successfully!')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to stop scheduler: {str(e)}')
            )

    def show_status(self, verbose=False):
        """
        Show scheduler status
        """
        try:
            from authentication.scheduler import get_scheduler
            
            scheduler = get_scheduler()
            
            status = "Running" if scheduler.is_running else "Stopped"
            self.stdout.write(f'Scheduler Status: {status}')
            
            if verbose and scheduler.is_running:
                jobs = scheduler.list_jobs()
                self.stdout.write(f'Active Jobs: {len(jobs)}')
                
                for job in jobs:
                    self.stdout.write(f'  - {job["id"]}: {job["name"]}')
                    self.stdout.write(f'    Next run: {job["next_run"]}')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to get scheduler status: {str(e)}')
            )

    def list_jobs(self, verbose=False):
        """
        List all scheduled jobs
        """
        try:
            from authentication.scheduler import get_scheduler
            
            scheduler = get_scheduler()
            
            if not scheduler.is_running:
                self.stdout.write(
                    self.style.WARNING('Scheduler is not running')
                )
                return
            
            jobs = scheduler.list_jobs()
            
            if not jobs:
                self.stdout.write('No scheduled jobs found')
                return
            
            self.stdout.write(f'Found {len(jobs)} scheduled job(s):')
            
            for job in jobs:
                self.stdout.write(f'\n{job["id"]}:')
                self.stdout.write(f'  Name: {job["name"]}')
                self.stdout.write(f'  Next run: {job["next_run"]}')
                self.stdout.write(f'  Trigger: {job["trigger"]}')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to list jobs: {str(e)}')
            )

    def test_scheduler(self, job_id=None, verbose=False):
        """
        Test scheduler functionality
        """
        if verbose:
            self.stdout.write('Testing scheduler functionality...')
        
        try:
            # Test scheduler configuration
            self.stdout.write('\n1. Testing scheduler configuration...')
            scheduler_config = getattr(settings, 'SCHEDULER_CONFIG', {})
            scheduled_jobs = getattr(settings, 'SCHEDULED_JOBS', [])
            
            self.stdout.write(f'   Scheduler config: {"Found" if scheduler_config else "Not found"}')
            self.stdout.write(f'   Scheduled jobs: {len(scheduled_jobs)} configured')
            
            # Test scheduler instance
            self.stdout.write('\n2. Testing scheduler instance...')
            from authentication.scheduler import get_scheduler
            
            scheduler = get_scheduler()
            self.stdout.write(f'   Scheduler status: {"Running" if scheduler.is_running else "Stopped"}')
            
            # Test token cleanup service
            self.stdout.write('\n3. Testing token cleanup service...')
            from authentication.services.token_cleanup_service import TokenCleanupService
            
            stats = TokenCleanupService.get_token_statistics()
            self.stdout.write(f'   Token statistics: {stats}')
            
            # Test specific job if requested
            if job_id:
                self.stdout.write(f'\n4. Testing specific job: {job_id}...')
                job_info = scheduler.get_job_info(job_id)
                if job_info:
                    self.stdout.write(f'   Job info: {job_info}')
                else:
                    self.stdout.write(f'   Job not found: {job_id}')
            
            # Test manual job execution
            self.stdout.write('\n5. Testing manual job execution...')
            confirm = input('Do you want to run token cleanup manually? (y/N): ')
            if confirm.lower() == 'y':
                result = TokenCleanupService.run_midnight_cleanup()
                self.stdout.write(f'   Cleanup result: {result.get("success", False)}')
                self.stdout.write(f'   Tokens deleted: {result.get("total_deleted", 0)}')
            else:
                self.stdout.write('   Manual execution skipped')
            
            self.stdout.write(
                self.style.SUCCESS('\nScheduler functionality test completed!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Scheduler test failed: {str(e)}')
            )
